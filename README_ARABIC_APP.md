# Arabic Text Memorization App

A React Native application for Arabic text memorization using AI-powered speech recognition. This app helps users memorize Arabic texts (Quran, poetry, hadith) through interactive practice sessions with real-time speech verification.

## Features

- **Arabic Text Display**: Proper RTL (right-to-left) text rendering with Arabic font support
- **Interactive Text Selection**: Select words or phrases for memorization practice
- **AI Speech Recognition**: Uses Whisper.rn for accurate Arabic speech recognition
- **Progressive Revelation**: Words are revealed as you speak them correctly
- **Real-time Feedback**: Immediate feedback on pronunciation accuracy
- **Offline Capability**: Works without internet once the model is loaded
- **Multiple Text Sources**: Includes Quran verses, Arabic poetry, and hadith

## Technical Stack

- **Framework**: React Native 0.80.2
- **Language**: TypeScript
- **Speech Recognition**: whisper.rn with ggml-base-q8_0 model
- **Package Manager**: Bun
- **State Management**: React Hooks and Context
- **Permissions**: react-native-permissions
- **Testing**: Jest with custom Arabic text utilities

## Prerequisites

Before running this app, make sure you have:

1. **React Native Development Environment** set up according to the [official guide](https://reactnative.dev/docs/environment-setup)
2. **Bun** package manager installed
3. **Android Studio** (for Android development) or **Xcode** (for iOS development)
4. **Physical device or emulator** with microphone support

## Installation

1. **Install dependencies**:
   ```bash
   bun install
   ```

2. **iOS Setup** (iOS only):
   ```bash
   cd ios && pod install && cd ..
   ```

## Running the App

### Start Metro Bundler
```bash
bun start
```

### Run on Android
```bash
bun run android
```

### Run on iOS
```bash
bun run ios
```

## Project Structure

```
src/
├── components/          # React Native components
│   ├── ArabicTextDisplay.tsx    # Main text display component
│   └── PracticeMode.tsx         # Practice session component
├── services/           # Business logic services
│   └── SpeechRecognitionService.ts  # Whisper.rn integration
├── utils/              # Utility functions
│   ├── arabicTextUtils.ts       # Arabic text processing
│   └── __tests__/              # Unit tests
├── types/              # TypeScript type definitions
│   └── index.ts
└── hooks/              # Custom React hooks

assets/
└── model/
    └── ggml-base-q8_0.bin      # Whisper AI model
```

## Key Components

### ArabicTextDisplay
- Renders Arabic text with proper RTL support
- Handles text selection for practice
- Shows/hides words based on practice state
- Supports word-level and sentence-level segmentation

### PracticeMode
- Manages practice sessions
- Integrates with speech recognition
- Provides real-time feedback
- Tracks progress and accuracy

### SpeechRecognitionService
- Wraps whisper.rn functionality
- Handles Arabic speech recognition
- Manages audio permissions
- Provides real-time transcription

### Arabic Text Utils
- Text normalization and comparison
- Diacritic handling
- Word and sentence segmentation
- RTL text direction detection

## Usage

1. **Select Text**: Choose from predefined Arabic texts (Quran, poetry, hadith)
2. **Choose Words**: Tap on words you want to practice memorizing
3. **Start Practice**: Begin the practice session
4. **Speak**: Recite the selected text - words will be revealed as you speak them correctly
5. **Complete**: Finish the session and see your accuracy score

## Permissions

The app requires microphone permission for speech recognition:

- **Android**: `RECORD_AUDIO` permission in AndroidManifest.xml
- **iOS**: `NSMicrophoneUsageDescription` in Info.plist

## Testing

Run the test suite:
```bash
bun test
```

Run specific tests:
```bash
bun test src/utils/__tests__/arabicTextUtils.test.ts
```

## Configuration

### Speech Recognition Settings
- **Language**: Arabic (ar)
- **Model**: ggml-base-q8_0.bin (included)
- **VAD**: Voice Activity Detection enabled
- **Real-time**: 30-second sessions with 5-second slices

### Arabic Text Processing
- **RTL Support**: Automatic text direction detection
- **Diacritics**: Automatic removal for comparison
- **Normalization**: Text normalization for accurate matching
- **Segmentation**: Word and sentence-level text splitting

## Troubleshooting

### Common Issues

1. **Microphone Permission Denied**
   - Check app permissions in device settings
   - Ensure microphone hardware is available

2. **Speech Recognition Not Working**
   - Verify the Whisper model is properly bundled
   - Check Metro configuration for .bin file support
   - Ensure device has sufficient memory

3. **Arabic Text Not Displaying Correctly**
   - Verify RTL support is enabled
   - Check font support for Arabic characters
   - Ensure proper text encoding

4. **Build Errors**
   - Run `bun install` to ensure all dependencies are installed
   - For iOS: `cd ios && pod install`
   - Clean build: `bun run android --reset-cache`

### Performance Optimization

- Use Release mode for better speech recognition performance
- Ensure sufficient device memory (recommended: 2GB+ RAM)
- Close other apps during practice sessions

## Implementation Status

✅ **Completed Features:**
- Project setup with React Native and TypeScript
- Whisper.rn integration with Arabic model
- Arabic text processing utilities
- RTL text display component
- Interactive text selection
- Practice mode with speech recognition
- Real-time feedback system
- Microphone permissions handling
- Comprehensive test suite

🔄 **Next Steps for Full Implementation:**
- Native module linking for whisper.rn
- Device testing with actual speech recognition
- UI/UX refinements and Arabic font integration
- Performance optimization for mobile devices
- Additional Arabic text sources
- User progress persistence
- Advanced speech recognition features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Acknowledgments

- **whisper.rn**: For providing excellent React Native integration with OpenAI's Whisper
- **OpenAI Whisper**: For the powerful speech recognition model
- **React Native Community**: For the robust mobile development framework
