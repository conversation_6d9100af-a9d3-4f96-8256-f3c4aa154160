import { initWhisper, WhisperContext } from 'whisper.rn';
import { Platform } from 'react-native';
import {
  SpeechRecognitionResult,
  SpeechRecognitionOptions,
  SpeechRecognitionState,
  ErrorType,
  AppError
} from '../types';

export class SpeechRecognitionService {
  private whisperContext: WhisperContext | null = null;
  private isInitialized = false;
  private currentSubscription: (() => void) | null = null;
  private listeners: Array<(state: SpeechRecognitionState) => void> = [];

  constructor() {
    this.initializeWhisper();
  }

  private async initializeWhisper(): Promise<void> {
    try {
      // Initialize Whisper with the provided model
      this.whisperContext = await initWhisper({
        filePath: require('../../assets/model/ggml-base-q8_0.bin'),
        isBundleAsset: true,
        useGpu: Platform.OS === 'ios', // Use GPU acceleration on iOS
      });

      this.isInitialized = true;
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
      });
    } catch (error) {
      console.error('Failed to initialize Whisper:', error);
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: 'Failed to initialize speech recognition model',
      });
      throw new AppError({
        type: ErrorType.MODEL_LOADING_FAILED,
        message: 'Failed to initialize Whisper model',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async startListening(options: SpeechRecognitionOptions = {}): Promise<void> {
    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      this.notifyListeners({
        isListening: true,
        isProcessing: false,
      });

      const transcribeOptions = {
        language: options.language || 'ar', // Default to Arabic
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        useVad: options.useVad !== false, // Enable VAD by default
        realtimeAudioSec: options.realtimeAudioSec || 30,
        realtimeAudioSliceSec: options.realtimeAudioSliceSec || 5,
        // Arabic-specific optimizations
        temperature: 0.0, // More deterministic results
        beamSize: 5,
        bestOf: 5,
        wordThold: 0.01,
        tokenTimestamps: true,
      };

      const { stop, subscribe } = await this.whisperContext.transcribeRealtime(transcribeOptions);

      this.currentSubscription = stop;

      subscribe((event) => {
        const { isCapturing, data, processTime, recordingTime, error } = event;

        if (error) {
          this.notifyListeners({
            isListening: false,
            isProcessing: false,
            error: `Speech recognition error: ${error}`,
          });
          return;
        }

        this.notifyListeners({
          isListening: isCapturing,
          isProcessing: !isCapturing && Boolean(data?.result),
          currentResult: data?.result ? {
            text: data.result,
            confidence: this.calculateConfidence(data),
            segments: data.segments?.map(segment => ({
              text: segment.text,
              start: segment.t0,
              end: segment.t1,
            })),
          } : undefined,
        });

        if (!isCapturing && data?.result) {
          // Final result received
          console.log(`Speech recognition completed in ${processTime}ms`);
          console.log(`Recording time: ${recordingTime}ms`);
        }
      });

    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      this.notifyListeners({
        isListening: false,
        isProcessing: false,
        error: 'Failed to start speech recognition',
      });
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to start speech recognition',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public async stopListening(): Promise<void> {
    if (this.currentSubscription) {
      try {
        await this.currentSubscription();
        this.currentSubscription = null;
        this.notifyListeners({
          isListening: false,
          isProcessing: false,
        });
      } catch (error) {
        console.error('Failed to stop speech recognition:', error);
        this.notifyListeners({
          isListening: false,
          isProcessing: false,
          error: 'Failed to stop speech recognition',
        });
      }
    }
  }

  public async transcribeAudio(audioPath: string, options: SpeechRecognitionOptions = {}): Promise<SpeechRecognitionResult> {
    if (!this.isInitialized || !this.whisperContext) {
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Speech recognition service not initialized',
        timestamp: new Date(),
      });
    }

    try {
      const transcribeOptions = {
        language: options.language || 'ar',
        maxThreads: options.maxThreads || (Platform.OS === 'android' ? 2 : 4),
        temperature: 0.0,
        beamSize: 5,
        bestOf: 5,
        wordThold: 0.01,
        tokenTimestamps: true,
      };

      const { promise } = this.whisperContext.transcribe(audioPath, transcribeOptions);
      const result = await promise;

      return {
        text: result.result,
        confidence: this.calculateConfidence(result),
        segments: result.segments?.map(segment => ({
          text: segment.text,
          start: segment.t0,
          end: segment.t1,
        })),
      };
    } catch (error) {
      console.error('Failed to transcribe audio:', error);
      throw new AppError({
        type: ErrorType.SPEECH_RECOGNITION_FAILED,
        message: 'Failed to transcribe audio',
        details: error,
        timestamp: new Date(),
      });
    }
  }

  public addListener(listener: (state: SpeechRecognitionState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public async release(): Promise<void> {
    try {
      await this.stopListening();
      if (this.whisperContext) {
        await this.whisperContext.release();
        this.whisperContext = null;
      }
      this.isInitialized = false;
      this.listeners = [];
    } catch (error) {
      console.error('Failed to release speech recognition service:', error);
    }
  }

  private calculateConfidence(data: any): number {
    // Simple confidence calculation based on available data
    // This can be enhanced with more sophisticated algorithms
    if (!data || !data.segments) {
      return 0.5; // Default confidence
    }

    const avgConfidence = data.segments.reduce((sum: number, segment: any) => {
      return sum + (segment.p || 0.5);
    }, 0) / data.segments.length;

    return Math.max(0, Math.min(1, avgConfidence));
  }

  private notifyListeners(state: SpeechRecognitionState): void {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in speech recognition listener:', error);
      }
    });
  }

  public get isReady(): boolean {
    return this.isInitialized && this.whisperContext !== null;
  }
}

// Singleton instance
export const speechRecognitionService = new SpeechRecognitionService();
