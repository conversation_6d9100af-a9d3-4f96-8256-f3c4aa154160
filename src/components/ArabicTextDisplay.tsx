import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  I18nManager,
} from 'react-native';
import { ArabicText, TextSegment, ArabicTextDisplayProps } from '../types';
import {
  createTextSegments,
  replaceWithPlaceholders,
  getTextDirection,
  splitIntoWords
} from '../utils/arabicTextUtils';

// Enable RTL layout
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

interface ArabicTextDisplayState {
  selectedSegments: TextSegment[];
  isSelecting: boolean;
  selectionStart: number;
  selectionEnd: number;
}

export const ArabicTextDisplay: React.FC<ArabicTextDisplayProps> = ({
  text,
  segments: propSegments,
  onSegmentSelect,
  onSelectionChange,
}) => {
  const [state, setState] = useState<ArabicTextDisplayState>({
    selectedSegments: [],
    isSelecting: false,
    selectionStart: -1,
    selectionEnd: -1,
  });

  // Create segments if not provided
  const segments = useMemo(() => {
    return propSegments || createTextSegments(text.content, 'word');
  }, [text.content, propSegments]);

  // Get text direction
  const textDirection = useMemo(() => getTextDirection(text.content), [text.content]);

  // Create display text with placeholders for hidden segments
  const displayText = useMemo(() => {
    return replaceWithPlaceholders(text.content, segments);
  }, [text.content, segments]);

  // Split text into renderable words with their positions
  const renderableWords = useMemo(() => {
    const words = splitIntoWords(text.content);
    const result: Array<{
      word: string;
      index: number;
      segment?: TextSegment;
      isHidden: boolean;
      isRevealed: boolean;
    }> = [];

    let currentIndex = 0;
    words.forEach((word, wordIndex) => {
      const wordStart = text.content.indexOf(word, currentIndex);
      const wordEnd = wordStart + word.length;

      // Find corresponding segment
      const segment = segments.find(s =>
        s.startIndex <= wordStart && s.endIndex >= wordEnd
      );

      result.push({
        word,
        index: wordIndex,
        segment,
        isHidden: segment?.isSelected && !segment?.isRevealed || false,
        isRevealed: segment?.isRevealed || false,
      });

      currentIndex = wordEnd;
    });

    return result;
  }, [text.content, segments]);

  const handleWordPress = useCallback((wordData: typeof renderableWords[0]) => {
    if (wordData.segment) {
      // Toggle segment selection
      const updatedSegment = {
        ...wordData.segment,
        isSelected: !wordData.segment.isSelected,
      };

      onSegmentSelect(updatedSegment);
    }
  }, [onSegmentSelect]);

  const handleWordLongPress = useCallback((wordData: typeof renderableWords[0]) => {
    // Start selection mode
    setState(prev => ({
      ...prev,
      isSelecting: true,
      selectionStart: wordData.index,
      selectionEnd: wordData.index,
    }));
  }, []);

  const renderWord = useCallback((wordData: typeof renderableWords[0], index: number) => {
    const { word, segment, isHidden, isRevealed } = wordData;

    let wordStyle: any[] = [styles.word];
    let displayWord = word;

    if (isHidden) {
      wordStyle.push(styles.hiddenWord);
      displayWord = '...';
    } else if (isRevealed) {
      wordStyle.push(styles.revealedWord);
    } else if (segment?.isSelected) {
      wordStyle.push(styles.selectedWord);
    }

    return (
      <TouchableOpacity
        key={`word-${index}`}
        style={wordStyle}
        onPress={() => handleWordPress(wordData)}
        onLongPress={() => handleWordLongPress(wordData)}
        activeOpacity={0.7}
      >
        <Text style={[styles.wordText, { writingDirection: textDirection }]}>
          {displayWord}
        </Text>
      </TouchableOpacity>
    );
  }, [handleWordPress, handleWordLongPress, textDirection]);

  const renderTitle = () => (
    <View style={styles.titleContainer}>
      <Text style={[styles.title, { writingDirection: textDirection }]}>
        {text.title}
      </Text>
      {text.source && (
        <Text style={[styles.source, { writingDirection: textDirection }]}>
          {text.source}
        </Text>
      )}
    </View>
  );

  const renderTextContent = () => (
    <View style={[styles.textContainer, { direction: textDirection }]}>
      <View style={styles.wordsContainer}>
        {renderableWords.map((wordData, index) => renderWord(wordData, index))}
      </View>
    </View>
  );

  const renderSelectionInfo = () => {
    const selectedCount = segments.filter(s => s.isSelected).length;
    const revealedCount = segments.filter(s => s.isRevealed).length;
    const totalCount = segments.length;

    if (selectedCount === 0) return null;

    return (
      <View style={styles.selectionInfo}>
        <Text style={styles.selectionText}>
          المحدد: {selectedCount} من {totalCount} كلمة
        </Text>
        {revealedCount > 0 && (
          <Text style={styles.progressText}>
            المكتشف: {revealedCount} من {selectedCount}
          </Text>
        )}
      </View>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      {renderTitle()}
      {renderTextContent()}
      {renderSelectionInfo()}
    </ScrollView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  titleContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'System', // Will be replaced with Arabic font
  },
  source: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  textContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  word: {
    marginHorizontal: 2,
    marginVertical: 1,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  wordText: {
    fontSize: 18,
    lineHeight: 28,
    color: '#2C3E50',
    fontFamily: 'System', // Will be replaced with Arabic font
  },
  selectedWord: {
    backgroundColor: '#3498DB',
  },
  hiddenWord: {
    backgroundColor: '#ECF0F1',
  },
  revealedWord: {
    backgroundColor: '#2ECC71',
  },
  selectionInfo: {
    backgroundColor: '#E8F4FD',
    borderRadius: 8,
    padding: 15,
    marginTop: 10,
  },
  selectionText: {
    fontSize: 16,
    color: '#2980B9',
    textAlign: 'center',
    marginBottom: 5,
  },
  progressText: {
    fontSize: 14,
    color: '#27AE60',
    textAlign: 'center',
  },
});

export default ArabicTextDisplay;
