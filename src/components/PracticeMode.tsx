import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
  Platform,
} from 'react-native';
import {
  PracticeModeProps,
  SpeechRecognitionState,
  SpeechRecognitionResult,
  PracticeProgress
} from '../types';
import { speechRecognitionService } from '../services/SpeechRecognitionService';
import {
  compareArabicTexts,
  findBestMatch,
  splitIntoWords,
  normalizeArabicText
} from '../utils/arabicTextUtils';

interface PracticeModeState {
  isActive: boolean;
  currentWordIndex: number;
  recognitionState: SpeechRecognitionState;
  progress: PracticeProgress;
  showFeedback: boolean;
  feedbackMessage: string;
  feedbackType: 'success' | 'error' | 'info';
}

export const PracticeMode: React.FC<PracticeModeProps> = ({
  session,
  onWordRevealed,
  onSessionComplete,
  onSessionPause,
  onSessionReset,
}) => {
  const [state, setState] = useState<PracticeModeState>({
    isActive: false,
    currentWordIndex: 0,
    recognitionState: {
      isListening: false,
      isProcessing: false,
    },
    progress: {
      totalWords: 0,
      revealedWords: 0,
      currentWordIndex: 0,
      accuracy: 0,
      timeElapsed: 0,
    },
    showFeedback: false,
    feedbackMessage: '',
    feedbackType: 'info',
  });

  const startTimeRef = useRef<Date | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Get all words from selected segments
  const targetWords = React.useMemo(() => {
    return session.selectedSegments
      .filter(segment => segment.isSelected)
      .flatMap(segment => splitIntoWords(segment.text));
  }, [session.selectedSegments]);

  // Initialize progress
  useEffect(() => {
    setState(prev => ({
      ...prev,
      progress: {
        ...prev.progress,
        totalWords: targetWords.length,
        revealedWords: session.selectedSegments.filter(s => s.isRevealed).length,
      },
    }));
  }, [targetWords.length, session.selectedSegments]);

  // Setup speech recognition listener
  useEffect(() => {
    const removeListener = speechRecognitionService.addListener((recognitionState) => {
      setState(prev => ({
        ...prev,
        recognitionState,
      }));

      // Process speech recognition results
      if (recognitionState.currentResult && !recognitionState.isListening) {
        handleSpeechResult(recognitionState.currentResult);
      }
    });

    return removeListener;
  }, []);

  // Timer for tracking elapsed time
  useEffect(() => {
    if (state.isActive) {
      startTimeRef.current = new Date();
      timerRef.current = setInterval(() => {
        if (startTimeRef.current) {
          const elapsed = Date.now() - startTimeRef.current.getTime();
          setState(prev => ({
            ...prev,
            progress: {
              ...prev.progress,
              timeElapsed: Math.floor(elapsed / 1000),
            },
          }));
        }
      }, 1000);
    } else {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [state.isActive]);

  // Pulse animation for microphone
  useEffect(() => {
    if (state.recognitionState.isListening) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    }
  }, [state.recognitionState.isListening, pulseAnim]);

  const handleSpeechResult = useCallback((result: SpeechRecognitionResult) => {
    const spokenText = normalizeArabicText(result.text);
    const currentTargetWord = targetWords[state.currentWordIndex];

    if (!currentTargetWord) return;

    // Find best match for the spoken text
    const match = findBestMatch(spokenText, [currentTargetWord], 0.7);

    if (match && match.similarity > 0.7) {
      // Successful match
      handleCorrectWord();
    } else {
      // Check if spoken text contains the target word
      const similarity = compareArabicTexts(spokenText, currentTargetWord);
      if (similarity > 0.5) {
        showFeedback('قريب جداً! حاول مرة أخرى', 'info');
      } else {
        showFeedback('لم يتم التعرف على الكلمة. حاول مرة أخرى', 'error');
      }
    }
  }, [state.currentWordIndex, targetWords]);

  const handleCorrectWord = useCallback(() => {
    // Vibrate on success
    if (Platform.OS === 'ios') {
      Vibration.vibrate(100);
    } else {
      Vibration.vibrate(100);
    }

    // Show success feedback
    showFeedback('ممتاز!', 'success');

    // Update progress
    const newWordIndex = state.currentWordIndex + 1;
    const newRevealedWords = state.progress.revealedWords + 1;

    setState(prev => ({
      ...prev,
      currentWordIndex: newWordIndex,
      progress: {
        ...prev.progress,
        revealedWords: newRevealedWords,
        currentWordIndex: newWordIndex,
        accuracy: (newRevealedWords / (newWordIndex + 1)) * 100,
      },
    }));

    // Notify parent component
    onWordRevealed(state.currentWordIndex);

    // Check if session is complete
    if (newWordIndex >= targetWords.length) {
      handleSessionComplete();
    }
  }, [state.currentWordIndex, state.progress.revealedWords, targetWords.length, onWordRevealed]);

  const showFeedback = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    setState(prev => ({
      ...prev,
      showFeedback: true,
      feedbackMessage: message,
      feedbackType: type,
    }));

    // Animate feedback
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(2000),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setState(prev => ({
        ...prev,
        showFeedback: false,
      }));
    });
  }, [fadeAnim]);

  const handleStartPractice = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isActive: true }));
      await speechRecognitionService.startListening({
        language: 'ar',
        useVad: true,
        realtimeAudioSec: 30,
        realtimeAudioSliceSec: 5,
      });
      showFeedback('ابدأ بقراءة النص', 'info');
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('خطأ', 'فشل في بدء التدريب. تأكد من إعطاء إذن الميكروفون.');
      setState(prev => ({ ...prev, isActive: false }));
    }
  }, []);

  const handlePausePractice = useCallback(async () => {
    try {
      await speechRecognitionService.stopListening();
      setState(prev => ({ ...prev, isActive: false }));
      onSessionPause();
    } catch (error) {
      console.error('Failed to pause practice:', error);
    }
  }, [onSessionPause]);

  const handleResetPractice = useCallback(() => {
    Alert.alert(
      'إعادة تعيين',
      'هل تريد إعادة تعيين التدريب؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'إعادة تعيين',
          style: 'destructive',
          onPress: async () => {
            await speechRecognitionService.stopListening();
            setState(prev => ({
              ...prev,
              isActive: false,
              currentWordIndex: 0,
              progress: {
                ...prev.progress,
                revealedWords: 0,
                currentWordIndex: 0,
                accuracy: 0,
                timeElapsed: 0,
              },
            }));
            onSessionReset();
          },
        },
      ]
    );
  }, [onSessionReset]);

  const handleSessionComplete = useCallback(async () => {
    await speechRecognitionService.stopListening();
    setState(prev => ({ ...prev, isActive: false }));

    Alert.alert(
      'تهانينا!',
      `لقد أكملت التدريب بنجاح!\nالدقة: ${state.progress.accuracy.toFixed(1)}%\nالوقت: ${Math.floor(state.progress.timeElapsed / 60)}:${(state.progress.timeElapsed % 60).toString().padStart(2, '0')}`,
      [
        { text: 'موافق', onPress: onSessionComplete },
      ]
    );
  }, [state.progress.accuracy, state.progress.timeElapsed, onSessionComplete]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderProgressBar = () => {
    const progressPercentage = (state.progress.revealedWords / state.progress.totalWords) * 100;

    return (
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          التقدم: {state.progress.revealedWords} من {state.progress.totalWords}
        </Text>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${progressPercentage}%` }
            ]}
          />
        </View>
        <Text style={styles.progressPercentage}>
          {progressPercentage.toFixed(1)}%
        </Text>
      </View>
    );
  };

  const renderControls = () => (
    <View style={styles.controlsContainer}>
      {!state.isActive ? (
        <TouchableOpacity
          style={[styles.button, styles.startButton]}
          onPress={handleStartPractice}
        >
          <Text style={styles.buttonText}>بدء التدريب</Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={[styles.button, styles.pauseButton]}
          onPress={handlePausePractice}
        >
          <Text style={styles.buttonText}>إيقاف مؤقت</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={[styles.button, styles.resetButton]}
        onPress={handleResetPractice}
      >
        <Text style={styles.buttonText}>إعادة تعيين</Text>
      </TouchableOpacity>
    </View>
  );

  const renderMicrophoneStatus = () => (
    <View style={styles.microphoneContainer}>
      <Animated.View
        style={[
          styles.microphoneIcon,
          {
            transform: [{ scale: pulseAnim }],
            backgroundColor: state.recognitionState.isListening ? '#E74C3C' : '#95A5A6',
          }
        ]}
      >
        <Text style={styles.microphoneText}>🎤</Text>
      </Animated.View>
      <Text style={styles.microphoneStatus}>
        {state.recognitionState.isListening
          ? 'جاري الاستماع...'
          : state.recognitionState.isProcessing
          ? 'جاري المعالجة...'
          : 'في انتظار الصوت'}
      </Text>
    </View>
  );

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statLabel}>الوقت</Text>
        <Text style={styles.statValue}>{formatTime(state.progress.timeElapsed)}</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statLabel}>الدقة</Text>
        <Text style={styles.statValue}>{state.progress.accuracy.toFixed(1)}%</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statLabel}>الكلمة الحالية</Text>
        <Text style={styles.statValue}>
          {state.currentWordIndex < targetWords.length
            ? targetWords[state.currentWordIndex]
            : 'مكتمل'}
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderProgressBar()}
      {renderStats()}
      {renderMicrophoneStatus()}
      {renderControls()}

      {state.showFeedback && (
        <Animated.View
          style={[
            styles.feedbackContainer,
            state.feedbackType === 'success' ? styles.feedbackSuccess :
            state.feedbackType === 'error' ? styles.feedbackError : styles.feedbackInfo,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.feedbackText}>{state.feedbackMessage}</Text>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8F9FA',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: '#2C3E50',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#ECF0F1',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3498DB',
  },
  progressPercentage: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
    color: '#7F8C8D',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    marginBottom: 5,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  microphoneContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  microphoneIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  microphoneText: {
    fontSize: 30,
  },
  microphoneStatus: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#27AE60',
  },
  pauseButton: {
    backgroundColor: '#F39C12',
  },
  resetButton: {
    backgroundColor: '#E74C3C',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  feedbackContainer: {
    position: 'absolute',
    top: '50%',
    left: 20,
    right: 20,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  feedbackSuccess: {
    backgroundColor: '#2ECC71',
  },
  feedbackError: {
    backgroundColor: '#E74C3C',
  },
  feedbackInfo: {
    backgroundColor: '#3498DB',
  },
  feedbackText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default PracticeMode;
