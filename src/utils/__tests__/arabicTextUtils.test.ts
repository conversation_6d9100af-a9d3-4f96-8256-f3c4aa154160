import {
  isArabicCharacter,
  isArabicDiacritic,
  removeDiacritics,
  normalizeArabicText,
  splitIntoWords,
  splitIntoSentences,
  createTextSegments,
  compareArabicTexts,
  findBestMatch,
  createPlaceholder,
  replaceWithPlaceholders,
  getTextDirection,
} from '../arabicTextUtils';

describe('Arabic Text Utils', () => {
  describe('isArabicCharacter', () => {
    it('should identify Arabic characters correctly', () => {
      expect(isArabicCharacter('ا')).toBe(true);
      expect(isArabicCharacter('ب')).toBe(true);
      expect(isArabicCharacter('ت')).toBe(true);
      expect(isArabicCharacter('a')).toBe(false);
      expect(isArabicCharacter('1')).toBe(false);
      expect(isArabicCharacter(' ')).toBe(false);
    });
  });

  describe('isArabicDiacritic', () => {
    it('should identify Arabic diacritics correctly', () => {
      expect(isArabicDiacritic('\u064E')).toBe(true); // Fatha
      expect(isArabicDiacritic('\u064F')).toBe(true); // Damma
      expect(isArabicDiacritic('\u0650')).toBe(true); // Kasra
      expect(isArabicDiacritic('ا')).toBe(false);
      expect(isArabicDiacritic('a')).toBe(false);
    });
  });

  describe('removeDiacritics', () => {
    it('should remove Arabic diacritics from text', () => {
      const textWithDiacritics = 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ';
      const textWithoutDiacritics = 'بسم الله الرحمن الرحيم';
      expect(removeDiacritics(textWithDiacritics)).toBe(textWithoutDiacritics);
    });

    it('should leave text without diacritics unchanged', () => {
      const text = 'بسم الله الرحمن الرحيم';
      expect(removeDiacritics(text)).toBe(text);
    });
  });

  describe('normalizeArabicText', () => {
    it('should normalize Arabic text correctly', () => {
      const text = '  بِسْمِ   اللَّهِ  ';
      const normalized = normalizeArabicText(text);
      expect(normalized).toBe('بسم الله');
    });

    it('should handle Persian and Arabic-Indic numerals', () => {
      const text = '۱۲۳ ٤٥٦';
      const normalized = normalizeArabicText(text);
      expect(normalized).toBe('123 456');
    });
  });

  describe('splitIntoWords', () => {
    it('should split Arabic text into words correctly', () => {
      const text = 'بسم الله الرحمن الرحيم';
      const words = splitIntoWords(text);
      expect(words).toEqual(['بسم', 'الله', 'الرحمن', 'الرحيم']);
    });

    it('should handle punctuation correctly', () => {
      const text = 'السلام عليكم، كيف حالك؟';
      const words = splitIntoWords(text);
      expect(words).toEqual(['السلام', 'عليكم', 'كيف', 'حالك']);
    });
  });

  describe('splitIntoSentences', () => {
    it('should split Arabic text into sentences correctly', () => {
      const text = 'السلام عليكم. كيف حالك؟ أنا بخير.';
      const sentences = splitIntoSentences(text);
      expect(sentences).toEqual(['السلام عليكم', 'كيف حالك', 'أنا بخير']);
    });
  });

  describe('createTextSegments', () => {
    it('should create word segments correctly', () => {
      const text = 'بسم الله';
      const segments = createTextSegments(text, 'word');
      
      expect(segments).toHaveLength(2);
      expect(segments[0].text).toBe('بسم');
      expect(segments[1].text).toBe('الله');
      expect(segments[0].isSelected).toBe(false);
      expect(segments[0].isRevealed).toBe(false);
    });
  });

  describe('compareArabicTexts', () => {
    it('should return 1.0 for identical texts', () => {
      const text1 = 'بسم الله';
      const text2 = 'بسم الله';
      expect(compareArabicTexts(text1, text2)).toBe(1.0);
    });

    it('should return 1.0 for texts that differ only in diacritics', () => {
      const text1 = 'بِسْمِ اللَّهِ';
      const text2 = 'بسم الله';
      expect(compareArabicTexts(text1, text2)).toBe(1.0);
    });

    it('should return a value between 0 and 1 for similar texts', () => {
      const text1 = 'بسم الله';
      const text2 = 'بسم';
      const similarity = compareArabicTexts(text1, text2);
      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThan(1);
    });

    it('should return 0 for completely different texts', () => {
      const text1 = 'بسم الله';
      const text2 = 'hello world';
      const similarity = compareArabicTexts(text1, text2);
      expect(similarity).toBeLessThan(0.5);
    });
  });

  describe('findBestMatch', () => {
    it('should find the best matching word', () => {
      const spokenWord = 'الله';
      const targetWords = ['بسم', 'الله', 'الرحمن'];
      const match = findBestMatch(spokenWord, targetWords);
      
      expect(match).not.toBeNull();
      expect(match?.word).toBe('الله');
      expect(match?.index).toBe(1);
      expect(match?.similarity).toBe(1.0);
    });

    it('should return null when no good match is found', () => {
      const spokenWord = 'hello';
      const targetWords = ['بسم', 'الله', 'الرحمن'];
      const match = findBestMatch(spokenWord, targetWords);
      
      expect(match).toBeNull();
    });
  });

  describe('createPlaceholder', () => {
    it('should create placeholders for Arabic text', () => {
      const text = 'بسم الله الرحمن';
      const placeholder = createPlaceholder(text);
      expect(placeholder).toBe('... ... ...');
    });
  });

  describe('getTextDirection', () => {
    it('should return rtl for Arabic text', () => {
      const arabicText = 'بسم الله الرحمن الرحيم';
      expect(getTextDirection(arabicText)).toBe('rtl');
    });

    it('should return ltr for English text', () => {
      const englishText = 'Hello world';
      expect(getTextDirection(englishText)).toBe('ltr');
    });

    it('should return rtl for mixed text with more Arabic', () => {
      const mixedText = 'بسم الله hello الرحمن';
      expect(getTextDirection(mixedText)).toBe('rtl');
    });
  });
});
