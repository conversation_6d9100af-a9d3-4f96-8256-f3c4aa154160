import { TextSegment, ArabicText } from '../types';

/**
 * Arabic text processing utilities
 */

// Arabic Unicode ranges
const ARABIC_RANGES = [
  [0x0600, 0x06FF], // Arabic
  [0x0750, 0x077F], // Arabic Supplement
  [0x08A0, 0x08FF], // Arabic Extended-A
  [0xFB50, 0xFDFF], // Arabic Presentation Forms-A
  [0xFE70, 0xFEFF], // Arabic Presentation Forms-B
];

// Arabic diacritics (Tashkeel)
const ARABIC_DIACRITICS = [
  '\u064B', // Fathatan
  '\u064C', // Dammatan
  '\u064D', // Kasratan
  '\u064E', // Fatha
  '\u064F', // Damma
  '\u0650', // Ka<PERSON>ra
  '\u0651', // Shadda
  '\u0652', // Sukun
  '\u0653', // Maddah
  '\u0654', // <PERSON>za above
  '\u0655', // <PERSON>za below
  '\u0656', // Subscript alef
  '\u0657', // Inverted damma
  '\u0658', // Mark noon ghunna
  '\u0659', // Zwarakay
  '\u065A', // Vowel sign small v above
  '\u065B', // Vowel sign inverted small v above
  '\u065C', // Vowel sign dot below
  '\u065D', // Reversed damma
  '\u065E', // Fatha with two dots
  '\u065F', // Wavy hamza below
  '\u0670', // Superscript alef
];

/**
 * Check if a character is Arabic
 */
export function isArabicCharacter(char: string): boolean {
  const code = char.charCodeAt(0);
  return ARABIC_RANGES.some(([start, end]) => code >= start && code <= end);
}

/**
 * Check if a character is an Arabic diacritic
 */
export function isArabicDiacritic(char: string): boolean {
  return ARABIC_DIACRITICS.includes(char);
}

/**
 * Remove Arabic diacritics from text
 */
export function removeDiacritics(text: string): string {
  return text.replace(new RegExp(`[${ARABIC_DIACRITICS.join('')}]`, 'g'), '');
}

/**
 * Normalize Arabic text for comparison
 */
export function normalizeArabicText(text: string): string {
  return removeDiacritics(text)
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/[۰-۹]/g, (match) => String.fromCharCode(match.charCodeAt(0) - '۰'.charCodeAt(0) + '0'.charCodeAt(0))) // Persian to Arabic numerals
    .replace(/[٠-٩]/g, (match) => String.fromCharCode(match.charCodeAt(0) - '٠'.charCodeAt(0) + '0'.charCodeAt(0))) // Arabic-Indic to Arabic numerals
    .replace(/ي/g, 'ی') // Normalize Yeh
    .replace(/ك/g, 'ک') // Normalize Kaf
    .replace(/ة/g, 'ه') // Normalize Teh Marbuta
    .toLowerCase();
}

/**
 * Split Arabic text into words
 */
export function splitIntoWords(text: string): string[] {
  // Split by whitespace and punctuation, keeping Arabic text intact
  return text
    .split(/[\s\u060C\u061B\u061F\u0640\u066A-\u066D\u06D4]+/)
    .filter(word => word.length > 0);
}

/**
 * Split Arabic text into sentences
 */
export function splitIntoSentences(text: string): string[] {
  // Split by Arabic sentence terminators
  return text
    .split(/[.!?؟۔\u06D4]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0);
}

/**
 * Create text segments from Arabic text
 */
export function createTextSegments(text: string, segmentType: 'word' | 'sentence' = 'word'): TextSegment[] {
  const segments: TextSegment[] = [];
  let currentIndex = 0;

  if (segmentType === 'word') {
    const words = splitIntoWords(text);
    let searchIndex = 0;

    words.forEach((word, index) => {
      const wordIndex = text.indexOf(word, searchIndex);
      if (wordIndex !== -1) {
        segments.push({
          id: `word-${index}`,
          text: word,
          startIndex: wordIndex,
          endIndex: wordIndex + word.length,
          isSelected: false,
          isRevealed: false,
        });
        searchIndex = wordIndex + word.length;
      }
    });
  } else {
    const sentences = splitIntoSentences(text);
    let searchIndex = 0;

    sentences.forEach((sentence, index) => {
      const sentenceIndex = text.indexOf(sentence, searchIndex);
      if (sentenceIndex !== -1) {
        segments.push({
          id: `sentence-${index}`,
          text: sentence,
          startIndex: sentenceIndex,
          endIndex: sentenceIndex + sentence.length,
          isSelected: false,
          isRevealed: false,
        });
        searchIndex = sentenceIndex + sentence.length;
      }
    });
  }

  return segments;
}

/**
 * Compare two Arabic texts for similarity
 */
export function compareArabicTexts(text1: string, text2: string): number {
  const normalized1 = normalizeArabicText(removeDiacritics(text1));
  const normalized2 = normalizeArabicText(removeDiacritics(text2));

  if (normalized1 === normalized2) {
    return 1.0; // Perfect match
  }

  // Calculate similarity using Levenshtein distance
  const distance = levenshteinDistance(normalized1, normalized2);
  const maxLength = Math.max(normalized1.length, normalized2.length);

  if (maxLength === 0) return 1.0;

  return 1 - (distance / maxLength);
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Find the best matching word in a text for a given spoken word
 */
export function findBestMatch(spokenWord: string, targetWords: string[], threshold: number = 0.7): { word: string; similarity: number; index: number } | null {
  let bestMatch = null;
  let bestSimilarity = 0;
  let bestIndex = -1;

  targetWords.forEach((word, index) => {
    const similarity = compareArabicTexts(spokenWord, word);
    if (similarity > bestSimilarity && similarity >= threshold) {
      bestMatch = word;
      bestSimilarity = similarity;
      bestIndex = index;
    }
  });

  return bestMatch ? { word: bestMatch, similarity: bestSimilarity, index: bestIndex } : null;
}

/**
 * Create placeholder text for hidden segments
 */
export function createPlaceholder(originalText: string): string {
  const words = splitIntoWords(originalText);
  return words.map(() => '...').join(' ');
}

/**
 * Replace text segments with placeholders
 */
export function replaceWithPlaceholders(text: string, segments: TextSegment[]): string {
  let result = text;

  // Sort segments by start index in descending order to avoid index shifting
  const sortedSegments = [...segments]
    .filter(segment => segment.isSelected && !segment.isRevealed)
    .sort((a, b) => b.startIndex - a.startIndex);

  sortedSegments.forEach(segment => {
    const placeholder = createPlaceholder(segment.text);
    result = result.substring(0, segment.startIndex) +
             placeholder +
             result.substring(segment.endIndex);
  });

  return result;
}

/**
 * Get text direction for proper RTL rendering
 */
export function getTextDirection(text: string): 'rtl' | 'ltr' {
  const arabicCharCount = text.split('').filter(isArabicCharacter).length;
  const totalChars = text.replace(/\s/g, '').length;

  // If more than 50% of characters are Arabic, use RTL
  return arabicCharCount / totalChars > 0.5 ? 'rtl' : 'ltr';
}

/**
 * Sample Arabic texts for the application
 */
export const SAMPLE_ARABIC_TEXTS: ArabicText[] = [
  {
    id: 'quran-fatiha',
    title: 'سورة الفاتحة',
    content: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ الرَّحْمَٰنِ الرَّحِيمِ مَالِكِ يَوْمِ الدِّينِ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ',
    source: 'القرآن الكريم',
    category: 'قرآن',
  },
  {
    id: 'poetry-mutanabbi',
    title: 'من شعر المتنبي',
    content: 'على قدر أهل العزم تأتي العزائم وتأتي على قدر الكرام المكارم وتعظم في عين الصغير صغارها وتصغر في عين العظيم العظائم',
    source: 'أبو الطيب المتنبي',
    category: 'شعر',
  },
  {
    id: 'hadith-actions',
    title: 'حديث الأعمال بالنيات',
    content: 'إنما الأعمال بالنيات وإنما لكل امرئ ما نوى فمن كانت هجرته إلى الله ورسوله فهجرته إلى الله ورسوله ومن كانت هجرته لدنيا يصيبها أو امرأة ينكحها فهجرته إلى ما هاجر إليه',
    source: 'صحيح البخاري',
    category: 'حديث',
  },
];
